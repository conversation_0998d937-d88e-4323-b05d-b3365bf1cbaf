#[cfg(feature = "recorder")]
mod tests {
    use liveion::config::{RecorderConfig, StorageConfig, S3Config};
    use liveion::recorder::{create_storage_operator, should_record};

#[test]
#[cfg(feature = "recorder")]
fn test_fs_storage_config_validation() {
    let config = StorageConfig {
        r#type: "fs".to_string(),
        root: "/tmp/recordings".to_string(),
        s3: None,
    };

    assert!(config.validate().is_ok());
}

    #[test]
    fn test_fs_storage_config_validation_empty_root() {
        let config = StorageConfig {
            r#type: "fs".to_string(),
            root: "".to_string(),
            s3: None,
        };
        
        assert!(config.validate().is_err());
    }

    #[test]
    fn test_s3_storage_config_validation() {
        let s3_config = S3Config {
            bucket: "test-bucket".to_string(),
            region: "us-east-1".to_string(),
            access_key_id: "test-key".to_string(),
            secret_access_key: "test-secret".to_string(),
            endpoint: None,
            enable_virtual_host_style: false,
        };

        let config = StorageConfig {
            r#type: "s3".to_string(),
            root: "recordings".to_string(),
            s3: Some(s3_config),
        };
        
        assert!(config.validate().is_ok());
    }

    #[test]
    fn test_s3_storage_config_validation_missing_config() {
        let config = StorageConfig {
            r#type: "s3".to_string(),
            root: "recordings".to_string(),
            s3: None,
        };
        
        assert!(config.validate().is_err());
    }

    #[test]
    fn test_s3_config_validation_missing_bucket() {
        let s3_config = S3Config {
            bucket: "".to_string(),
            region: "us-east-1".to_string(),
            access_key_id: "test-key".to_string(),
            secret_access_key: "test-secret".to_string(),
            endpoint: None,
            enable_virtual_host_style: false,
        };

        assert!(s3_config.validate().is_err());
    }

    #[test]
    fn test_recorder_config_validation() {
        let storage_config = StorageConfig {
            r#type: "fs".to_string(),
            root: "/tmp/recordings".to_string(),
            s3: None,
        };

        let recorder_config = RecorderConfig {
            auto_streams: vec!["test-*".to_string()],
            storage: storage_config,
        };
        
        assert!(recorder_config.validate().is_ok());
    }

    #[test]
    fn test_unsupported_storage_type() {
        let config = StorageConfig {
            r#type: "unsupported".to_string(),
            root: "/tmp/recordings".to_string(),
            s3: None,
        };
        
        let result = config.validate();
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("Unsupported storage type"));
    }

    #[tokio::test]
    async fn test_create_fs_storage_operator() {
        let config = StorageConfig {
            r#type: "fs".to_string(),
            root: "/tmp".to_string(),
            s3: None,
        };
        
        let result = create_storage_operator(&config).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_create_s3_storage_operator() {
        let s3_config = S3Config {
            bucket: "test-bucket".to_string(),
            region: "us-east-1".to_string(),
            access_key_id: "test-key".to_string(),
            secret_access_key: "test-secret".to_string(),
            endpoint: Some("http://localhost:9000".to_string()),
            enable_virtual_host_style: false,
        };

        let config = StorageConfig {
            r#type: "s3".to_string(),
            root: "recordings".to_string(),
            s3: Some(s3_config),
        };
        
        // Note: This will fail without actual S3 credentials, but tests the operator creation
        let result = create_storage_operator(&config).await;
        // We expect this to succeed in creating the operator, even if it can't connect
        assert!(result.is_ok());
    }

    #[test]
    fn test_should_record_patterns() {
        let patterns = vec![
            "stream-*".to_string(),
            "important-*".to_string(),
            "meeting-room-*".to_string(),
        ];

        assert!(should_record(&patterns, "stream-123"));
        assert!(should_record(&patterns, "important-event"));
        assert!(should_record(&patterns, "meeting-room-a"));
        assert!(!should_record(&patterns, "test-stream"));
        assert!(!should_record(&patterns, "random"));
    }

    #[test]
    fn test_should_record_wildcard() {
        let patterns = vec!["*".to_string()];
        
        assert!(should_record(&patterns, "any-stream"));
        assert!(should_record(&patterns, "test"));
        assert!(should_record(&patterns, "123"));
    }

    #[test]
    fn test_should_record_empty_patterns() {
        let patterns = vec![];
        
        assert!(!should_record(&patterns, "any-stream"));
    }

    #[test]
    fn test_configuration_parsing() {
        // Test that TOML configuration can be parsed correctly
        let config_str = r#"
[http]
listen = "[::]:7777"

[[ice_servers]]
urls = ["stun:stun.l.google.com:19302"]

[recorder]
auto_streams = ["test-*"]

[recorder.storage]
type = "fs"
root = "./recordings"

[log]
level = "info"
"#;

        let config: liveion::config::Config = toml::from_str(config_str).unwrap();
        assert!(config.validate().is_ok());
        assert_eq!(config.recorder.storage.r#type, "fs");
        assert_eq!(config.recorder.storage.root, "./recordings");
    }

    #[test]
    fn test_s3_configuration_parsing() {
        let config_str = r#"
[http]
listen = "[::]:7777"

[[ice_servers]]
urls = ["stun:stun.l.google.com:19302"]

[recorder]
auto_streams = ["prod-*"]

[recorder.storage]
type = "s3"
root = "recordings"

[recorder.storage.s3]
bucket = "test-bucket"
region = "us-east-1"
access_key_id = "test-key"
secret_access_key = "test-secret"
enable_virtual_host_style = false

[log]
level = "info"
"#;

        let config: liveion::config::Config = toml::from_str(config_str).unwrap();
        assert!(config.validate().is_ok());
        assert_eq!(config.recorder.storage.r#type, "s3");
        
        let s3_config = config.recorder.storage.s3.unwrap();
        assert_eq!(s3_config.bucket, "test-bucket");
        assert_eq!(s3_config.region, "us-east-1");
    }
}
