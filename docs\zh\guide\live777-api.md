# Live777 HTTP API

## WHIP && WHEP

`POST` `/whip/:streamId`

Response: [201]

`POST` `/whep/:streamId`

Response: [201]

* * *

`PATCH` `/session/:streamId/:sessionId`

Response: [204]

`DELETE` `/session/:streamId/:sessionId`

Response: [204]

## Stream

### 创建一个流

`POST` `/api/streams/:streamId`

`streamId` 需要唯一标识符​​

你可以使用此配置自动创建流​​

```toml
[strategy]
# WHIP auto a stream
auto_create_whip = true
# WHEP auto a stream
auto_create_whep = true
```

Response: [204]

### Get all Stream

`GET` `/api/streams/`

Response: [200]

- `id`: String, `streamId`
- `createdAt`: Int, `timestamp`
- `publish`: `Object(PubSub)`, about publisher
- `subscribe`: `Object(PubSub)`, about subscriber
- `(publish | subscribe).leaveAt`: Int, `timestamp`
- `(publish | subscribe).sessions`: Array, `sessions`
- `(publish | subscribe).sessions.[].id`: String, `sessionId`
- `(publish | subscribe).sessions.[].createdAt`: Int, `timestamp`
- `(publish | subscribe).sessions.[].state`: String, [RTCPeerConnection/connectionState](https://developer.mozilla.org/en-US/docs/Web/API/RTCPeerConnection/connectionState#value)
- `(publish | subscribe).sessions.[].cascade`: Optional(Object(Cascade))
- `(publish | subscribe).sessions.[].cascade.sourceUrl`: Optional(String(URL))
- `(publish | subscribe).sessions.[].cascade.targetUrl`: Optional(String(URL))
- `(publish | subscribe).sessions.[].cascade.sessionUrl`: String(URL)

例如:

```json
[
  {
    "id": "push",
    "createdAt": 1719326206862,
    "publish": {
      "leaveAt": 0,
      "sessions": [
        {
          "id": "08c1f2a0a60b0deeb66ee572bd369f80",
          "createdAt": 1719326206947,
          "state": "connected"
        }
      ]
    },
    "subscribe": {
      "leaveAt": 1719326206862,
      "sessions": []
    }
  },
  {
    "id": "pull",
    "createdAt": 1719326203854,
    "publish": {
      "leaveAt": 0,
      "sessions": [
        {
          "id": "41b2c52da4fb1eed5a3bff9a9a200d80",
          "createdAt": 1719326205079,
          "state": "connected",
          "cascade": {
            "sourceUrl": "http://localhost:7777/whep/web-0",
            "sessionUrl": "http://localhost:7777/session/web-0/aabc02240abfc7f4800e8d9a6f087808"
          }
        }
      ]
    },
    "subscribe": {
      "leaveAt": 1719326203854,
      "sessions": []
    }
  },
  {
    "id": "web-0",
    "createdAt": 1719326195910,
    "publish": {
      "leaveAt": 0,
      "sessions": [
        {
          "id": "0dc47d8da8eb0a64fe40f461f47c2a36",
          "createdAt": 1719326196264,
          "state": "connected"
        }
      ]
    },
    "subscribe": {
      "leaveAt": 0,
      "sessions": [
        {
          "id": "aabc02240abfc7f4800e8d9a6f087808",
          "createdAt": 1719326204997,
          "state": "connected"
        },
        {
          "id": "dab1a9e88b2400cfd4bcfb4487588ef3",
          "createdAt": 1719326206798,
          "state": "connected",
          "cascade": {
            "targetUrl": "http://localhost:7777/whip/push",
            "sessionUrl": "http://localhost:7777/session/push/08c1f2a0a60b0deeb66ee572bd369f80"
          }
        },
        {
          "id": "685beee8650b761116b581a4a87ca9b9",
          "createdAt": 1719326228314,
          "state": "connected"
        }
      ]
    }
  }
]
```

### 销毁一个流

`DELETE` `/api/streams/:streamId`

Response: [204]

## ​级联

`POST` `/api/cascade/:streamId`

Request:

```json
{
  "token": "",
  "sourceUrl": "",
  "targetUrl": "",
}
```

- `token`: Option, auth header
- `sourceUrl`: `Option<WHEP url>`. if has, use pull mode
- `targetUrl`: `Option<WHIP url>`. if has, use push mode
- `sourceUrl` and `targetUrl` at the same time can only one


## 录制

> **注意**: 录制功能是可选特性，需要在编译时启用 `recorder` 特性才能使用。

### 开始录制流

`POST` `/api/streams/:streamId/record`

开始录制指定的流。录制功能会将 WebRTC 流转换为 MPEG-DASH 格式的分片文件，并存储到配置的存储后端（本地文件系统或 S3）。

Request:

```


- `streamId`: 要录制的流ID（URL路径参数）

**响应:**

成功: `[204 No Content]`

错误响应:
- `[400 Bad Request]` - 流不存在或没有活跃发布者
- `[500 Internal Server Error]` - 录制功能未启用或配置错误

**示例:**

```bash
curl -X POST http://localhost:7777/api/streams/my-stream/record
```

**录制行为说明:**

1. **自动停止**: 录制会在流结束时自动停止，无需手动干预
2. **重复调用**: 如果流已经在录制中，重复调用此API会返回成功但不会创建新的录制任务
3. **输出格式**: 录制文件以 MPEG-DASH 格式存储，按日期组织目录结构：`{stream}/{yyyy}/{MM}/{dd}/`
4. **存储位置**: 根据配置文件中的 `[recorder.storage]` 设置决定存储位置

**自动录制:**

除了手动调用API，还可以通过配置文件启用自动录制：

```toml
[recorder]
auto_streams = ["prod-*", "important-*"]  # 匹配这些模式的流会自动开始录制
```

**录制文件访问:**

录制完成后，文件会按以下目录结构存储：

```
/recordings
└──/{streamId}
   └──/{yyyy}/{MM}/{dd}
      ├── manifest.mpd      # MPEG-DASH 清单文件
      ├── seg_0001.m4s      # 视频分片
      ├── seg_0002.m4s
      ├── audio_seg_0001.m4s # 音频分片
      └── audio_seg_0002.m4s
```

可以通过 HTTP 服务器直接访问这些文件，或使用提供的 Python 服务器脚本：

```bash
cd records
python server.py 8000  # 在端口8000启动HTTP服务器
```

**相关配置:**

详细的录制器配置说明请参考 [Recorder 配置文档](./recorder.md)。

**当前限制:**

- 不支持手动停止录制（只能等待流结束自动停止）
- 不提供录制状态查询API
- 不提供录制任务列表查询API
- 不支持录制参数配置（如分辨率、码率等）
- 录制文件访问需要额外的HTTP服务器


