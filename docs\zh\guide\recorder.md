# Recorder

Live777 可选的 Recorder 特性提供强大的实时流录制功能，支持将 WebRTC 流录制、转封装为 MPEG-DASH 格式的分片，并存储到多种存储后端。这是一个可选功能，需要在编译时启用 `recorder` 特性。

## 架构

录制器采用 **OpenDAL** (开放数据访问层) 作为统一的存储抽象，使得在不同存储提供商之间切换无需修改代码。

**处理流程**:
`WebRTC 流 → H.264/Opus 解码 → MP4 分片器 → OpenDAL → 存储后端 (本地/S3等)`


## 配置

在配置文件中添加 `[recorder]` 部分以启用此功能。

### 自动录制

```toml
[recorder]
auto_streams = ["prod-*"] # 自动录制的流名称模式
```

### 存储后端配置

#### 本地

```toml
[recorder.storage]
type = "fs"
root = "./recordings" # 录制文件根目录
```

#### Amazon S3 或 S3 兼容接口

```toml
[recorder.storage]
type = "s3"
root = "recordings" # 存储桶 (Bucket) 内的可选前缀

[recorder.storage.s3]
bucket = "my-live777-recordings"
region = "us-east-1"
access_key_id = "${AWS_ACCESS_KEY_ID}"
secret_access_key = "${AWS_SECRET_ACCESS_KEY}"
```

## 输出格式

录制文件按流名称和日期进行组织。

### 目录结构

```
/recordings
└──/stream-name
   └──/2024/01/15
      ├── manifest.mpd
      ├── seg_0001.m4s
      ├── seg_0002.m4s
      ├── audio_seg_0001.m4s
      └── audio_seg_0002.m4s
```

`manifest.mpd`: MPEG-DASH 清单文件，描述轨道、分片信息等。
`seg_XXXX.m4s`: 视频分片。
`audio_seg_XXXX.m4s`: 音频分片。
