<?xml version="1.0" encoding="utf-8"?>
<MPD xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
xmlns="urn:mpeg:dash:schema:mpd:2011"
xmlns:xlink="http://www.w3.org/1999/xlink"
xsi:schemaLocation="urn:mpeg:DASH:schema:MPD:2011 http://standards.iso.org/ittf/PubliclyAvailableStandards/MPEG-DASH_schema_files/DASH-MPD.xsd"
profiles="urn:mpeg:dash:profile:isoff-live:2011"
type="static"
mediaPresentationDuration="PT20.000S"
maxSegmentDuration="PT10S"
minBufferTime="PT30S">
<ProgramInformation/>
    <ServiceDescription id="0"/>
    <Period id="0" start="PT0.0S">
        <AdaptationSet id="0" contentType="video" startWithSAP="1" segmentAlignment="true" bitstreamSwitching="true" frameRate="30/1" maxWidth="640" maxHeight="480" par="4:3">
            <Representation id="0" mimeType="video/mp4" codecs="avc1.42401f" bandwidth="1658491" width="640" height="480" sar="1:1">
                <SegmentTemplate timescale="90000" initialization="init.m4s" media="seg_$Number%04d$.m4s" duration="900000" startNumber="1" />
            </Representation>
        </AdaptationSet>
                <AdaptationSet id="1" contentType="audio" segmentAlignment="true">
            <Representation id="1" mimeType="audio/mp4" codecs="opus" bandwidth="25458" audioSamplingRate="48000" >
                <SegmentTemplate timescale="48000" initialization="audio_init.m4s" media="audio_seg_$Number%04d$.m4s" duration="480000" startNumber="1" />
            </Representation>
        </AdaptationSet>

    </Period>
</MPD>
