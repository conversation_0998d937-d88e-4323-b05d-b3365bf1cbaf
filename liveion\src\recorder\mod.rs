use glob::Pattern;
use once_cell::sync::Lazy;
use opendal::services::{Fs, S3};
use opendal::Operator;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

use crate::hook::{Event, StreamEventType};
use crate::stream::manager::Manager;

#[cfg(feature = "recorder")]
use crate::config::RecorderConfig;

mod segmenter;
mod task;
use task::RecordingTask;
pub mod codec;
mod fmp4;

static TASKS: Lazy<RwLock<HashMap<String, RecordingTask>>> =
    Lazy::new(|| RwLock::new(HashMap::new()));

static STORAGE: Lazy<RwLock<Option<Operator>>> = Lazy::new(|| RwLock::new(None));

/// Initialize recorder event listener.
#[cfg(feature = "recorder")]
pub async fn init(manager: Arc<Manager>, cfg: RecorderConfig) {
    // Validate configuration first
    if let Err(e) = cfg.validate() {
        tracing::error!("[recorder] invalid configuration: {}", e);
        return;
    }

    let manager_clone = manager.clone();

    // Initialize storage Operator
    {
        let mut storage_writer = STORAGE.write().await;
        if storage_writer.is_none() {
            match create_storage_operator(&cfg.storage).await {
                Ok(op) => {
                    *storage_writer = Some(op);
                    tracing::info!("[recorder] initialized {} storage backend at: {}",
                        cfg.storage.r#type, cfg.storage.root);
                }
                Err(e) => {
                    tracing::error!("[recorder] init storage error: {}", e);
                    return;
                }
            }
        }
    }

    let cfg = Arc::new(cfg);
    let mut recv = manager.subscribe_event();
    tokio::spawn(async move {
        while let Ok(event) = recv.recv().await {
            if let Event::Stream(stream_event) = event {
                match stream_event.r#type {
                    StreamEventType::Up => {
                        let stream_name = stream_event.stream.stream;
                        if should_record(&cfg.auto_streams, &stream_name) {
                            if let Err(e) = start(manager_clone.clone(), stream_name.clone()).await
                            {
                                tracing::error!("[recorder] start failed: {}", e);
                            }
                        }
                    }
                    StreamEventType::Down => {
                        let stream_name = stream_event.stream.stream;
                        let mut map = TASKS.write().await;
                        if let Some(task) = map.remove(&stream_name) {
                            task.stop();
                            tracing::info!("[recorder] stop recording task for {}", stream_name);
                        }
                    }
                }
            }
        }
    });
}

/// Entry point for starting recording manually or automatically
pub async fn start(manager: Arc<Manager>, stream: String) -> anyhow::Result<()> {
    let mut map = TASKS.write().await;
    if map.contains_key(&stream) {
        tracing::info!("[recorder] stream {} is already recording", stream);
        return Ok(());
    }

    let task = RecordingTask::spawn(manager, &stream).await?;
    map.insert(stream.clone(), task);
    tracing::info!("[recorder] spawn recording task for {}", stream);
    Ok(())
}

pub fn should_record(patterns: &[String], stream: &str) -> bool {
    for p in patterns {
        if let Ok(pat) = Pattern::new(p) {
            if pat.matches(stream) {
                return true;
            }
        }
    }
    false
}

/// Create storage operator based on configuration
pub async fn create_storage_operator(config: &crate::config::StorageConfig) -> anyhow::Result<Operator> {
    match config.r#type.as_str() {
        "fs" => {
            let builder = Fs::default().root(&config.root);
            let op = Operator::new(builder)?.finish();
            Ok(op)
        }
        "s3" => {
            let s3_config = config.s3.as_ref()
                .ok_or_else(|| anyhow::anyhow!("S3 configuration is required when storage type is 's3'"))?;

            let mut builder = S3::default()
                .bucket(&s3_config.bucket)
                .region(&s3_config.region)
                .access_key_id(&s3_config.access_key_id)
                .secret_access_key(&s3_config.secret_access_key);

            // Set optional endpoint
            if let Some(endpoint) = &s3_config.endpoint {
                builder = builder.endpoint(endpoint);
            }

            // Set virtual host style
            if s3_config.enable_virtual_host_style {
                builder = builder.enable_virtual_host_style();
            }

            // Set root prefix if specified
            if !config.root.is_empty() {
                builder = builder.root(&config.root);
            }

            let op = Operator::new(builder)?.finish();
            Ok(op)
        }
        _ => {
            Err(anyhow::anyhow!("Unsupported storage type: {}", config.r#type))
        }
    }
}
